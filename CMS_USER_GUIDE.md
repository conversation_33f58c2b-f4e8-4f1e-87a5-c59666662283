# 📚 دليل استخدام لوحة إدارة المحتوى - موقع يوسف محمد الشخصي

## 🔗 الروابط المهمة:
- **الموقع المنشور**: https://youssef-personal-website.netlify.app
- **لوحة إدارة المحتوى**: https://youssef-personal-website.netlify.app/admin
- **مستودع GitHub**: https://github.com/elzaeem2/youssef-personal-website
- **لوحة تحكم Netlify**: https://app.netlify.com/projects/youssef-personal-website

---

## 🚀 البدء السريع

### 1. الدخول إلى لوحة الإدارة:
1. اذهب إلى: https://youssef-personal-website.netlify.app/admin
2. اضغط "Login with Netlify Identity"
3. أدخل بريدك الإلكتروني وكلمة المرور
4. ستظهر لك لوحة الإدارة باللغة العربية

### 2. أول مرة تسجيل دخول:
- إذا لم تكن مسجلاً بعد، ستحتاج إلى دعوة من مدير الموقع
- تحقق من بريدك الإلكتروني للحصول على رابط التفعيل
- أنشئ كلمة مرور قوية واحفظها في مكان آمن

---

## 📝 إدارة المحتوى

### 🏠 تحديث معلومات الصفحة الرئيسية:
1. اضغط على "الإعدادات" من القائمة الجانبية
2. اختر "الصفحة الرئيسية"
3. يمكنك تعديل:
   - عنوان الهيرو (العنوان الرئيسي)
   - العنوان الفرعي
   - وصف الهيرو
   - نصوص الأزرار
   - الإحصائيات

### 👤 تحديث المعلومات الشخصية:
1. اذهب إلى "الإعدادات" → "معلومات الموقع"
2. يمكنك تغيير:
   - اسم الموقع
   - الوصف العام
   - الاسم الشخصي
   - المسمى الوظيفي
   - **الصورة الشخصية** (ارفع صورة جديدة)
   - البريد الإلكتروني
   - رقم الهاتف
   - العنوان
   - روابط التواصل الاجتماعي

### 📄 تحديث صفحة السيرة الذاتية:
1. اذهب إلى "الإعدادات" → "صفحة من أنا"
2. يمكنك تعديل:
   - العنوان الرئيسي والفرعي
   - النص التعريفي (يدعم التنسيق المتقدم)
   - المعلومات الشخصية (العمر، الموقع، سنوات الخبرة)
   - المهارات (اسم المهارة، النسبة المئوية، الفئة)

---

## 💼 إدارة المشاريع

### ➕ إضافة مشروع جديد:
1. اضغط على "المشاريع" من القائمة
2. اضغط "New Projects" أو "مشروع جديد"
3. املأ المعلومات:
   - **العنوان**: اسم المشروع
   - **الوصف**: وصف مختصر
   - **الصورة**: ارفع صورة للمشروع
   - **التاريخ**: تاريخ إنجاز المشروع
   - **العميل**: اسم العميل (اختياري)
   - **التقنيات المستخدمة**: قائمة بالتقنيات
   - **رابط المشروع**: الرابط المباشر
   - **رابط المعاينة**: رابط للمعاينة
   - **الفئة**: اختر من (web, mobile, design, ecommerce)
   - **مميز**: لإظهار المشروع في المقدمة
   - **المحتوى**: وصف تفصيلي بتنسيق Markdown

### ✏️ تعديل مشروع موجود:
1. اضغط على المشروع من قائمة المشاريع
2. عدل المعلومات المطلوبة
3. اضغط "Save" أو "حفظ"

### 🗑️ حذف مشروع:
1. افتح المشروع للتعديل
2. اضغط "Delete" أو "حذف"
3. أكد الحذف

---

## 🛠️ إدارة الخدمات

### ➕ إضافة خدمة جديدة:
1. اضغط على "الخدمات"
2. اضغط "New Services" أو "خدمة جديدة"
3. املأ البيانات:
   - **العنوان**: اسم الخدمة
   - **الوصف القصير**: وصف مختصر
   - **الأيقونة**: كود Font Awesome (مثل: fas fa-code)
   - **السعر**: السعر بالعملة المفضلة
   - **الميزات**: قائمة بميزات الخدمة
   - **مميزة**: لإبراز الخدمة
   - **ترتيب العرض**: رقم لترتيب الخدمات
   - **المحتوى التفصيلي**: شرح مفصل

### 💡 نصائح للخدمات:
- استخدم أيقونات Font Awesome المناسبة
- اكتب أسعار واضحة ومفهومة
- رتب الخدمات حسب الأهمية
- استخدم ميزة "مميزة" للخدمات الأساسية

---

## 🛒 إدارة المنتجات

### ➕ إضافة منتج جديد:
1. اضغط على "المنتجات"
2. اضغط "New Products" أو "منتج جديد"
3. املأ المعلومات:
   - **اسم المنتج**: العنوان
   - **الوصف**: وصف المنتج
   - **صورة المنتج**: صورة توضيحية
   - **السعر الحالي**: السعر الفعلي
   - **السعر القديم**: للعروض (اختياري)
   - **الميزات**: قائمة بميزات المنتج
   - **التقنيات**: التقنيات المستخدمة
   - **رابط المعاينة**: للتجربة
   - **رابط الشراء**: رابط الشراء المباشر
   - **شارة المنتج**: "جديد" أو "الأكثر مبيعاً"
   - **متوفر**: حالة التوفر
   - **ترتيب العرض**: ترتيب المنتجات

---

## 🎨 نصائح التصميم والمحتوى

### 📸 الصور:
- استخدم صور عالية الجودة (1200x800 بكسل أو أكبر)
- تأكد من أن الصور محسنة للويب (أقل من 500KB)
- استخدم تنسيقات JPG للصور العادية و PNG للشعارات

### ✍️ النصوص:
- اكتب نصوص واضحة ومفهومة
- استخدم العناوين الفرعية لتنظيم المحتوى
- تجنب النصوص الطويلة جداً

### 🔗 الروابط:
- تأكد من صحة جميع الروابط
- استخدم روابط HTTPS دائماً
- اختبر الروابط بانتظام

---

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

**1. لا أستطيع تسجيل الدخول:**
- تأكد من صحة البريد الإلكتروني وكلمة المرور
- تحقق من بريدك للحصول على رابط إعادة تعيين كلمة المرور
- تأكد من تفعيل حسابك

**2. التغييرات لا تظهر في الموقع:**
- انتظر 2-3 دقائق بعد الحفظ
- حدث الصفحة (Ctrl+F5)
- تحقق من أن التغييرات محفوظة في لوحة الإدارة

**3. مشكلة في رفع الصور:**
- تأكد من أن حجم الصورة أقل من 10MB
- استخدم تنسيقات مدعومة (JPG, PNG, GIF)
- تأكد من اتصال الإنترنت

**4. خطأ في النشر:**
- تحقق من حالة النشر في Netlify
- تأكد من أن Git Gateway مفعل
- راجع سجل الأخطاء في Netlify

---

## 📞 الدعم والمساعدة

إذا واجهت أي مشاكل أو احتجت مساعدة:

1. **راجع هذا الدليل** أولاً للحلول الشائعة
2. **تحقق من حالة Netlify**: https://status.netlify.com
3. **راجع سجل النشر** في لوحة تحكم Netlify
4. **تواصل مع المطور** للدعم التقني

---

## 🎯 نصائح للنجاح

1. **حدث المحتوى بانتظام** لجذب الزوار
2. **استخدم كلمات مفتاحية مناسبة** لتحسين SEO
3. **اختبر الموقع على أجهزة مختلفة** للتأكد من التجاوب
4. **احتفظ بنسخة احتياطية** من المحتوى المهم
5. **راقب أداء الموقع** باستخدام Google Analytics

---

**تم إنشاء هذا الدليل خصيصاً لموقع يوسف محمد الشخصي**  
**آخر تحديث: يوليو 2025**
