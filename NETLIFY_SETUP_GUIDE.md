# دليل إعداد Netlify Identity و Git Gateway

## الخطوات المطلوبة في لوحة تحكم Netlify:

### 1. تفعيل Netlify Identity:
1. اذهب إلى: https://app.netlify.com/projects/youssef-personal-website
2. اضغط على تبويب "Identity"
3. اضغط "Enable Identity"
4. في إعدادات Registration، اختر "Invite only" أو "Open" حسب تفضيلك
5. في إعدادات External providers، يمكنك تفعيل Google أو GitHub للدخول السريع

### 2. تفعيل Git Gateway:
1. في نفس صفحة Identity
2. اذهب إلى "Services" 
3. اضغط "Enable Git Gateway"
4. سيطلب منك ربط GitHub - اضغط موافق

### 3. إنشاء مستخدم إداري:
1. في تبويب Identity
2. اضغط "Invite users"
3. أدخل بريدك الإلكتروني: <EMAIL>
4. اختر Role: Admin (إذا كان متاحاً)
5. اضغط "Send invite"

### 4. تأكيد الدعوة:
1. تحقق من بريدك الإلكتروني
2. اضغط على رابط التأكيد
3. أنشئ كلمة مرور قوية
4. سجل دخولك

## روابط مهمة:
- الموقع المنشور: https://youssef-personal-website.netlify.app
- لوحة إدارة المحتوى: https://youssef-personal-website.netlify.app/admin
- لوحة تحكم Netlify: https://app.netlify.com/projects/youssef-personal-website
- مستودع GitHub: https://github.com/elzaeem2/youssef-personal-website

## اختبار النظام:
1. اذهب إلى: https://youssef-personal-website.netlify.app/admin
2. سجل دخولك باستخدام بريدك الإلكتروني
3. جرب إضافة مشروع جديد أو تعديل المعلومات الشخصية
4. احفظ التغييرات وتأكد من ظهورها في الموقع

## ملاحظات مهمة:
- قد تحتاج إلى انتظار 2-3 دقائق بعد تفعيل Identity قبل أن تعمل لوحة الإدارة
- إذا واجهت مشاكل، تأكد من أن Git Gateway مفعل ومربوط بـ GitHub
- يمكنك دعوة مستخدمين إضافيين من تبويب Identity في Netlify
