# موقع شخصي متكامل وحديث

موقع إلكتروني شخصي متكامل وعصري مصمم لعرض الأعمال والمشاريع والخدمات بطريقة احترافية وجذابة.

## 🌟 المميزات

- **تصميم عصري ومتجاوب**: يعمل بشكل مثالي على جميع الأجهزة (كمبيوتر، تابلت، موبايل)
- **أداء سريع**: محسن للسرعة والأداء
- **سهولة التخصيص**: كود نظيف ومنظم
- **دعم اللغة العربية**: مصمم خصيصاً للمحتوى العربي
- **Netlify CMS**: نظام إدارة محتوى سهل الاستخدام
- **SEO محسن**: جاهز لمحركات البحث

## 📁 هيكل المشروع

```
├── index.html          # الصفحة الرئيسية
├── about.html          # صفحة السيرة الذاتية
├── projects.html       # صفحة الأعمال والمشاريع
├── services.html       # صفحة الخدمات
├── products.html       # صفحة المنتجات
├── contact.html        # صفحة التواصل
├── style.css           # ملف الأنماط الرئيسي
├── images/             # مجلد الصور
├── admin/              # إعدادات Netlify CMS
│   ├── index.html      # واجهة إدارة المحتوى
│   └── config.yml      # إعدادات CMS
└── content/            # ملفات المحتوى
    ├── projects/       # بيانات المشاريع
    ├── services/       # بيانات الخدمات
    ├── products/       # بيانات المنتجات
    └── settings/       # إعدادات الموقع
```

## 🎨 الألوان والتصميم

الموقع يستخدم لوحة ألوان تقنية حديثة:
- **اللون الأساسي**: أزرق (#2563eb)
- **اللون الثانوي**: أخضر (#10b981)
- **لون التمييز**: برتقالي (#f59e0b)
- **الخلفية**: رمادي داكن (#0f172a)
- **الأسطح**: رمادي متوسط (#1e293b)

## 🚀 كيفية الاستخدام

### 1. النشر على Netlify

1. ارفع الملفات إلى مستودع Git (GitHub, GitLab, etc.)
2. اربط المستودع بـ Netlify
3. فعل Netlify Identity للمصادقة
4. فعل Git Gateway للـ CMS

### 2. إعداد Netlify CMS

1. اذهب إلى `/admin` بعد النشر
2. سجل دخولك باستخدام Netlify Identity
3. ابدأ في إدارة المحتوى

### 3. تخصيص المحتوى

#### تحديث المعلومات الشخصية:
- اذهب إلى Admin → الإعدادات → معلومات الموقع
- حدث الاسم، المسمى الوظيفي، معلومات التواصل

#### إضافة مشاريع جديدة:
- اذهب إلى Admin → المشاريع → إضافة مشروع جديد
- املأ البيانات المطلوبة (العنوان، الوصف، الصورة، التقنيات)

#### إدارة الخدمات:
- اذهب إلى Admin → الخدمات
- أضف أو عدل الخدمات المقدمة

#### إدارة المنتجات:
- اذهب إلى Admin → المنتجات
- أضف منتجات رقمية جديدة مع الأسعار والميزات

## 🛠️ التخصيص المتقدم

### تغيير الألوان:
عدل المتغيرات في بداية ملف `style.css`:

```css
:root {
    --primary-color: #2563eb;
    --secondary-color: #10b981;
    --accent-color: #f59e0b;
    /* ... باقي المتغيرات */
}
```

### إضافة أقسام جديدة:
1. أضف HTML الجديد في الملف المناسب
2. أضف الأنماط في `style.css`
3. حدث إعدادات CMS في `admin/config.yml`

### تخصيص النماذج:
عدل نماذج المعاينة في `admin/index.html` لتناسب احتياجاتك.

## 📱 الاستجابة والتوافق

الموقع متوافق مع:
- ✅ جميع المتصفحات الحديثة
- ✅ الأجهزة المحمولة والتابلت
- ✅ شاشات عالية الدقة
- ✅ قارئات الشاشة (Accessibility)

## 🔧 التحسينات المستقبلية

- [ ] دمج Google Analytics
- [ ] إضافة نظام تعليقات
- [ ] دمج مع منصات التواصل الاجتماعي
- [ ] إضافة مدونة
- [ ] دعم اللغات المتعددة
- [ ] PWA (Progressive Web App)

## 📞 الدعم والمساعدة

إذا كنت بحاجة لمساعدة في التخصيص أو إضافة ميزات جديدة:

- 📧 البريد الإلكتروني: <EMAIL>
- 📱 الهاتف: +966 50 123 4567
- 💬 واتساب: متاح للاستشارات السريعة

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

---

**تم التطوير بـ ❤️ لخدمة المجتمع العربي التقني**
