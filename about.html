<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>السيرة الذاتية - موقعي الشخصي</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://identity.netlify.com/v1/netlify-identity-widget.js"></script>
</head>
<body>
    <!-- Header Navigation -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <h2>موقعي الشخصي</h2>
                </div>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="index.html" class="nav-link">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a href="about.html" class="nav-link active">السيرة الذاتية</a>
                    </li>
                    <li class="nav-item">
                        <a href="projects.html" class="nav-link">الأعمال والمشاريع</a>
                    </li>
                    <li class="nav-item">
                        <a href="services.html" class="nav-link">الخدمات</a>
                    </li>
                    <li class="nav-item">
                        <a href="products.html" class="nav-link">المنتجات</a>
                    </li>
                    <li class="nav-item">
                        <a href="contact.html" class="nav-link">تواصل</a>
                    </li>
                </ul>
                <div class="hamburger">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <h1 class="page-title">السيرة الذاتية</h1>
            <p class="page-subtitle">تعرف على خبراتي ومهاراتي المهنية</p>
        </div>
    </section>

    <!-- About Content -->
    <section class="about-content">
        <div class="container">
            <div class="about-grid">
                <div class="about-text">
                    <h2>من أنا؟</h2>
                    <p>
                        مرحباً، أنا محمد أحمد، مطور ويب ومصمم واجهات مستخدم بخبرة تزيد عن 5 سنوات في مجال تطوير المواقع والتطبيقات الحديثة. 
                        أتخصص في إنشاء تجارب رقمية متميزة باستخدام أحدث التقنيات والأدوات.
                    </p>
                    <p>
                        أؤمن بأن التصميم الجيد والكود النظيف هما أساس أي مشروع ناجح. أسعى دائماً لتقديم حلول مبتكرة تلبي احتياجات العملاء 
                        وتوفر تجربة مستخدم استثنائية.
                    </p>
                    <div class="personal-info">
                        <div class="info-item">
                            <i class="fas fa-user"></i>
                            <span>الاسم: يوسف محمد</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-birthday-cake"></i>
                            <span>العمر: 23 سنة</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>الموقع: الموصل، العراق</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-envelope"></i>
                            <span>البريد: <EMAIL></span>
                        </div>
                    </div>
                </div>
                <div class="about-image">
                    <div class="image-container">
                        <div class="profile-image">
                            <img src="images/photo_2025-07-18_00-04-58.jpg" alt="يوسف محمد" class="profile-img">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section class="skills-section">
        <div class="container">
            <h2 class="section-title">المهارات التقنية</h2>
            <div class="skills-grid">
                <div class="skill-category">
                    <h3>تطوير الواجهات الأمامية</h3>
                    <div class="skills-list">
                        <div class="skill-item">
                            <span class="skill-name">HTML5 & CSS3</span>
                            <div class="skill-bar">
                                <div class="skill-progress" style="width: 95%"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">JavaScript</span>
                            <div class="skill-bar">
                                <div class="skill-progress" style="width: 90%"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">React.js</span>
                            <div class="skill-bar">
                                <div class="skill-progress" style="width: 85%"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">Vue.js</span>
                            <div class="skill-bar">
                                <div class="skill-progress" style="width: 80%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="skill-category">
                    <h3>تطوير الواجهات الخلفية</h3>
                    <div class="skills-list">
                        <div class="skill-item">
                            <span class="skill-name">Node.js</span>
                            <div class="skill-bar">
                                <div class="skill-progress" style="width: 85%"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">PHP</span>
                            <div class="skill-bar">
                                <div class="skill-progress" style="width: 80%"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">Python</span>
                            <div class="skill-bar">
                                <div class="skill-progress" style="width: 75%"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">MySQL</span>
                            <div class="skill-bar">
                                <div class="skill-progress" style="width: 85%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="skill-category">
                    <h3>أدوات التصميم</h3>
                    <div class="skills-list">
                        <div class="skill-item">
                            <span class="skill-name">Figma</span>
                            <div class="skill-bar">
                                <div class="skill-progress" style="width: 90%"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">Adobe XD</span>
                            <div class="skill-bar">
                                <div class="skill-progress" style="width: 85%"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">Photoshop</span>
                            <div class="skill-bar">
                                <div class="skill-progress" style="width: 80%"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">Illustrator</span>
                            <div class="skill-bar">
                                <div class="skill-progress" style="width: 75%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Experience Section -->
    <section class="experience-section">
        <div class="container">
            <h2 class="section-title">الخبرة المهنية</h2>
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-date">2022 - الآن</div>
                    <div class="timeline-content">
                        <h3>مطور ويب أول - شركة التقنية المتقدمة</h3>
                        <p>قيادة فريق تطوير الواجهات الأمامية وتطوير تطبيقات ويب معقدة باستخدام React و Vue.js</p>
                        <ul>
                            <li>تطوير أكثر من 15 مشروع ويب متقدم</li>
                            <li>تحسين أداء التطبيقات بنسبة 40%</li>
                            <li>قيادة فريق من 5 مطورين</li>
                        </ul>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-date">2020 - 2022</div>
                    <div class="timeline-content">
                        <h3>مطور واجهات أمامية - شركة الحلول الرقمية</h3>
                        <p>تطوير واجهات مستخدم تفاعلية ومتجاوبة لمختلف أنواع المواقع والتطبيقات</p>
                        <ul>
                            <li>تطوير أكثر من 25 موقع إلكتروني</li>
                            <li>تحسين تجربة المستخدم وزيادة معدل التحويل</li>
                            <li>العمل مع فرق متعددة التخصصات</li>
                        </ul>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-date">2019 - 2020</div>
                    <div class="timeline-content">
                        <h3>مطور ويب مبتدئ - استوديو الإبداع</h3>
                        <p>بداية مسيرتي المهنية في تطوير المواقع الإلكترونية والتعلم من خبراء المجال</p>
                        <ul>
                            <li>تطوير مواقع بسيطة ومتوسطة التعقيد</li>
                            <li>تعلم أفضل الممارسات في البرمجة</li>
                            <li>المشاركة في مشاريع جماعية</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Education Section -->
    <section class="education-section">
        <div class="container">
            <h2 class="section-title">التعليم والشهادات</h2>
            <div class="education-grid">
                <div class="education-item">
                    <div class="education-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <h3>بكالوريوس علوم الحاسب</h3>
                    <p class="education-place">جامعة الملك سعود</p>
                    <p class="education-date">2015 - 2019</p>
                    <p>تخصص في هندسة البرمجيات وتطوير التطبيقات</p>
                </div>
                <div class="education-item">
                    <div class="education-icon">
                        <i class="fas fa-certificate"></i>
                    </div>
                    <h3>شهادة React Developer</h3>
                    <p class="education-place">Meta (Facebook)</p>
                    <p class="education-date">2021</p>
                    <p>شهادة متخصصة في تطوير تطبيقات React</p>
                </div>
                <div class="education-item">
                    <div class="education-icon">
                        <i class="fas fa-award"></i>
                    </div>
                    <h3>شهادة UX/UI Design</h3>
                    <p class="education-place">Google</p>
                    <p class="education-date">2020</p>
                    <p>تصميم تجربة وواجهة المستخدم</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>موقعي الشخصي</h3>
                    <p>مطور ويب ومصمم واجهات مستخدم متخصص في إنشاء تجارب رقمية متميزة</p>
                    <div class="social-links">
                        <a href="#" class="social-link"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-github"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>روابط سريعة</h4>
                    <ul class="footer-links">
                        <li><a href="about.html">السيرة الذاتية</a></li>
                        <li><a href="projects.html">الأعمال</a></li>
                        <li><a href="services.html">الخدمات</a></li>
                        <li><a href="contact.html">تواصل</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>معلومات التواصل</h4>
                    <div class="contact-info">
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-phone"></i> +966 50 123 4567</p>
                        <p><i class="fas fa-map-marker-alt"></i> الرياض، السعودية</p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 موقعي الشخصي. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script src="js/image-loader.js"></script>
    <script>
        // Mobile menu toggle
        const hamburger = document.querySelector('.hamburger');
        const navMenu = document.querySelector('.nav-menu');

        hamburger.addEventListener('click', () => {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });

        document.querySelectorAll('.nav-link').forEach(n => n.addEventListener('click', () => {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        }));
    </script>
</body>
</html>
