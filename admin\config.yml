backend:
  name: git-gateway
  branch: main

media_folder: "images"
public_folder: "/images"

site_url: https://youssef-personal-website.netlify.app

collections:
  # Site Settings
  - name: "settings"
    label: "Site Settings"
    files:
      - label: "Site Information"
        name: "site_info"
        file: "content/settings/site_info.yml"
        fields:
          - { label: "Site Title", name: "title", widget: "string" }
          - { label: "Description", name: "description", widget: "text" }
          - { label: "Phone", name: "phone", widget: "string" }
          - { label: "Email", name: "email", widget: "string" }
          - { label: "Address", name: "address", widget: "string" }
          - label: "Social Links"
            name: "social"
            widget: "object"
            fields:
              - { label: "Facebook", name: "facebook", widget: "string", required: false }
              - { label: "Twitter", name: "twitter", widget: "string", required: false }
              - { label: "LinkedIn", name: "linkedin", widget: "string", required: false }
              - { label: "GitHub", name: "github", widget: "string", required: false }

  # About Page
  - name: "about"
    label: "About"
    files:
      - label: "About Page"
        name: "about"
        file: "content/settings/about.yml"
        fields:
          - { label: "Name", name: "name", widget: "string" }
          - { label: "Title", name: "title", widget: "string" }
          - { label: "Bio", name: "bio", widget: "markdown" }
          - { label: "Photo", name: "photo", widget: "image" }
          - { label: "Resume PDF", name: "resume", widget: "file", required: false }

  # Projects
  - name: "projects"
    label: "Projects"
    folder: "content/projects"
    create: true
    slug: "{{slug}}"
    fields:
      - { label: "Title", name: "title", widget: "string" }
      - { label: "Description", name: "description", widget: "text" }
      - { label: "Image", name: "image", widget: "image" }
      - { label: "Technologies", name: "technologies", widget: "list" }
      - { label: "Live URL", name: "live_url", widget: "string", required: false }
      - { label: "GitHub URL", name: "github_url", widget: "string", required: false }
      - { label: "Content", name: "body", widget: "markdown" }

  # Services
  - name: "services"
    label: "Services"
    folder: "content/services"
    create: true
    slug: "{{slug}}"
    fields:
      - { label: "Title", name: "title", widget: "string" }
      - { label: "Description", name: "description", widget: "text" }
      - { label: "Price", name: "price", widget: "string" }
      - { label: "Features", name: "features", widget: "list" }
      - { label: "Content", name: "body", widget: "markdown" }
