---
title: "React Developer Certification"
issuer: "Meta (Facebook)"
date: "مارس 2023"
expiry_date: ""
credential_id: "META-REACT-2023-001"
verification_url: "https://coursera.org/verify/META-REACT-2023-001"
description: "شهادة متخصصة في تطوير تطبيقات React.js من Meta، تغطي المفاهيم المتقدمة والأدوات الحديثة"
skills:
  - "React.js"
  - "JSX"
  - "React Hooks"
  - "State Management"
  - "React Router"
  - "Testing"
  - "Performance Optimization"
image: ""
order: 1
---

حصلت على شهادة React Developer المعتمدة من Meta (Facebook) بعد إكمال برنامج تدريبي شامل يغطي جميع جوانب تطوير تطبيقات React.js الحديثة.

## محتوى البرنامج:

### الأساسيات
- مقدمة في React.js والمفاهيم الأساسية
- JSX وكيفية استخدامه بفعالية
- Components والتعامل مع Props

### المفاهيم المتقدمة
- React Hooks وإدارة الحالة
- Context API للبيانات المشتركة
- React Router للتنقل بين الصفحات

### الأدوات والتطوير
- Create React App وإعداد البيئة
- أدوات التطوير والتصحيح
- اختبار التطبيقات باستخدام Jest

### الأداء والتحسين
- تحسين أداء التطبيقات
- Code Splitting و Lazy Loading
- أفضل الممارسات في React

تم اجتياز جميع الاختبارات والمشاريع العملية بنجاح، مما يؤهلني للعمل على مشاريع React.js المعقدة والمتقدمة.
