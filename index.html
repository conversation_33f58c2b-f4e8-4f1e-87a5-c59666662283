<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصفحة الرئيسية - موقعي الشخصي</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://identity.netlify.com/v1/netlify-identity-widget.js"></script>
</head>
<body>
    <!-- Header Navigation -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <h2>موقعي الشخصي</h2>
                </div>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="index.html" class="nav-link active">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a href="about.html" class="nav-link">السيرة الذاتية</a>
                    </li>
                    <li class="nav-item">
                        <a href="projects.html" class="nav-link">الأعمال والمشاريع</a>
                    </li>
                    <li class="nav-item">
                        <a href="services.html" class="nav-link">الخدمات</a>
                    </li>
                    <li class="nav-item">
                        <a href="products.html" class="nav-link">المنتجات</a>
                    </li>
                    <li class="nav-item">
                        <a href="contact.html" class="nav-link">تواصل</a>
                    </li>
                </ul>
                <div class="hamburger">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <div class="hero-text">
                    <h1 class="hero-title">مرحباً، أنا <span class="highlight">يوسف محمد</span></h1>
                    <p class="hero-subtitle">مطور ويب ومصمم واجهات مستخدم</p>
                    <p class="hero-description">
                        أقوم بتطوير مواقع ويب حديثة وتطبيقات تفاعلية باستخدام أحدث التقنيات. 
                        أهتم بتجربة المستخدم والتصميم الأنيق والأداء المتميز.
                    </p>
                    <div class="hero-buttons">
                        <a href="projects.html" class="btn btn-primary">
                            <i class="fas fa-briefcase"></i>
                            أعمالي
                        </a>
                        <a href="contact.html" class="btn btn-secondary">
                            <i class="fas fa-envelope"></i>
                            تواصل معي
                        </a>
                    </div>
                </div>
                <div class="hero-image">
                    <div class="hero-profile-image">
                        <img src="images/photo_2025-07-18_00-04-58.jpg" alt="يوسف محمد" class="hero-img">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features">
        <div class="container">
            <h2 class="section-title">ماذا أقدم؟</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <h3>تطوير الويب</h3>
                    <p>تطوير مواقع ويب حديثة وسريعة باستخدام أحدث التقنيات والأدوات</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-paint-brush"></i>
                    </div>
                    <h3>تصميم واجهات</h3>
                    <p>تصميم واجهات مستخدم جذابة وسهلة الاستخدام مع تجربة مستخدم متميزة</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>تطبيقات الموبايل</h3>
                    <p>تطوير تطبيقات موبايل متجاوبة تعمل على جميع الأجهزة والمنصات</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <h3>الأداء والسرعة</h3>
                    <p>تحسين أداء المواقع والتطبيقات لضمان سرعة التحميل والاستجابة</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">25+</div>
                    <div class="stat-label">مشروع مكتمل</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">15+</div>
                    <div class="stat-label">عميل راضي</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">3+</div>
                    <div class="stat-label">سنوات خبرة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">24/7</div>
                    <div class="stat-label">دعم فني</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>موقعي الشخصي</h3>
                    <p>مطور ويب ومصمم واجهات مستخدم متخصص في إنشاء تجارب رقمية متميزة</p>
                    <div class="social-links">
                        <a href="#" class="social-link"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-github"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>روابط سريعة</h4>
                    <ul class="footer-links">
                        <li><a href="about.html">السيرة الذاتية</a></li>
                        <li><a href="projects.html">الأعمال</a></li>
                        <li><a href="services.html">الخدمات</a></li>
                        <li><a href="contact.html">تواصل</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>معلومات التواصل</h4>
                    <div class="contact-info">
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-phone"></i> +964 ************</p>
                        <p><i class="fas fa-map-marker-alt"></i> الموصل، العراق</p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 موقعي الشخصي. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script src="js/image-loader.js"></script>

    <script>
        // Netlify Identity Widget
        if (window.netlifyIdentity) {
            window.netlifyIdentity.on("init", user => {
                if (!user) {
                    window.netlifyIdentity.on("login", () => {
                        document.location.href = "/admin/";
                    });
                }
            });
        }
    </script>

    <script>
        // Mobile menu toggle
        const hamburger = document.querySelector('.hamburger');
        const navMenu = document.querySelector('.nav-menu');

        hamburger.addEventListener('click', () => {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });

        // Close mobile menu when clicking on a link
        document.querySelectorAll('.nav-link').forEach(n => n.addEventListener('click', () => {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        }));
    </script>
</body>
</html>
