[build]
  publish = "."
  command = "echo 'No build command needed for static site'"

[build.environment]
  NODE_VERSION = "18"

# Redirect rules
[[redirects]]
  from = "/admin/*"
  to = "/admin/index.html"
  status = 200

# Headers for security
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# Cache static assets
[[headers]]
  for = "/images/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

# Enable Netlify Identity
[context.production]
  environment = { NODE_ENV = "production" }

# Form handling
[build.processing]
  skip_processing = false

[build.processing.css]
  bundle = true
  minify = true

[build.processing.js]
  bundle = true
  minify = true

[build.processing.html]
  pretty_urls = true

# Enable Netlify Functions (if needed in future)
[functions]
  directory = "functions"
