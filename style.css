/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette - Modern Tech Theme */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #10b981;
    --accent-color: #f59e0b;
    --background-color: #0f172a;
    --surface-color: #1e293b;
    --surface-light: #334155;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --border-color: #475569;
    --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    --gradient-secondary: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    /* Typography */
    --font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    --spacing-16: 4rem;
    --spacing-20: 5rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

body {
    font-family: var(--font-family);
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased; /* Better font rendering on iOS */
    -moz-osx-font-smoothing: grayscale; /* Better font rendering on macOS */
    text-rendering: optimizeLegibility; /* Better text rendering */
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
}

/* Header and Navigation */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: var(--transition-normal);
}

.navbar {
    padding: var(--spacing-4) 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo h2 {
    color: var(--primary-color);
    font-size: var(--font-size-xl);
    font-weight: 700;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--spacing-8);
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-fast);
    position: relative;
    padding: var(--spacing-2) 0;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: var(--transition-normal);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* Mobile Menu */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: var(--spacing-1);
}

.bar {
    width: 25px;
    height: 3px;
    background-color: var(--text-primary);
    transition: var(--transition-fast);
    border-radius: var(--radius-sm);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, var(--background-color) 0%, var(--surface-color) 100%);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(37, 99, 235, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
    position: relative;
    z-index: 1;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-16);
    align-items: center;
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    margin-bottom: var(--spacing-4);
    line-height: 1.2;
}

.highlight {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    color: var(--secondary-color);
    margin-bottom: var(--spacing-6);
    font-weight: 600;
}

.hero-description {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-8);
    line-height: 1.7;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-4);
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-6);
    border-radius: var(--radius-lg);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition-normal);
    border: none;
    cursor: pointer;
    font-size: var(--font-size-base);
    min-height: 44px; /* Minimum touch target size for accessibility */
    touch-action: manipulation; /* Improve touch responsiveness */
    -webkit-tap-highlight-color: transparent; /* Remove tap highlight on iOS */
    user-select: none; /* Prevent text selection */
    line-height: 1.2;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: transparent;
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.btn-secondary:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.image-placeholder {
    width: 300px;
    height: 300px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 120px;
    color: white;
    box-shadow: var(--shadow-xl);
    animation: float 6s ease-in-out infinite;
}

.hero-profile-image {
    width: 300px;
    height: 300px;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    animation: float 6s ease-in-out infinite;
    position: relative;
}

.hero-profile-image::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: var(--gradient-primary);
    border-radius: 50%;
    z-index: -1;
    opacity: 0.3;
}

.hero-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    transition: var(--transition-normal);
}

.hero-img:hover {
    transform: scale(1.05);
}

/* Image Loading States */
.hero-img,
.profile-img {
    opacity: 1;
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}

.hero-img.loaded,
.profile-img.loaded {
    opacity: 1;
}

/* Fallback styles */
.profile-fallback {
    font-family: var(--font-family);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Ensure images maintain aspect ratio */
.hero-profile-image,
.profile-image {
    position: relative;
    overflow: hidden;
}

.hero-profile-image img,
.profile-image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Features Section */
.features {
    padding: var(--spacing-20) 0;
    background: var(--surface-color);
}

.section-title {
    text-align: center;
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-16);
    color: var(--text-primary);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-8);
}

.feature-card {
    background: var(--background-color);
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    text-align: center;
    transition: var(--transition-normal);
    border: 1px solid var(--border-color);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-6);
    font-size: var(--font-size-2xl);
    color: white;
}

.feature-card h3 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-4);
    color: var(--text-primary);
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Stats Section */
.stats {
    padding: var(--spacing-20) 0;
    background: var(--background-color);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-8);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-6);
}

.stat-number {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-2);
}

.stat-label {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
}

/* Footer */
.footer {
    background: var(--surface-color);
    padding: var(--spacing-16) 0 var(--spacing-8);
    border-top: 1px solid var(--border-color);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-8);
    margin-bottom: var(--spacing-8);
}

.footer-section h3,
.footer-section h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-4);
}

.footer-section p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-4);
}

.social-links {
    display: flex;
    gap: var(--spacing-3);
}

.social-link {
    width: 40px;
    height: 40px;
    background: var(--surface-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.social-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--spacing-2);
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.footer-links a:hover {
    color: var(--primary-color);
}

.contact-info p {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    margin-bottom: var(--spacing-2);
}

.contact-info i {
    color: var(--primary-color);
    width: 20px;
}

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-8);
    border-top: 1px solid var(--border-color);
    color: var(--text-muted);
}

/* Enhanced Responsive Design */

/* Large tablets and small desktops (768px - 1024px) */
@media (max-width: 1024px) {
    .container {
        padding: 0 var(--spacing-6);
    }

    .hero-title {
        font-size: var(--font-size-4xl);
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-6);
    }
}

/* Tablets (768px and below) */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--surface-color);
        width: 100%;
        text-align: center;
        transition: var(--transition-normal);
        box-shadow: var(--shadow-lg);
        padding: var(--spacing-4) 0;
        z-index: 999;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-item {
        margin: var(--spacing-3) 0;
    }

    .nav-link {
        padding: var(--spacing-3) var(--spacing-4);
        font-size: var(--font-size-lg);
    }

    .hamburger.active .bar:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .hamburger.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero {
        padding: var(--spacing-16) 0;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-8);
    }

    .hero-title {
        font-size: var(--font-size-3xl);
        line-height: 1.2;
        margin-bottom: var(--spacing-4);
    }

    .hero-subtitle {
        font-size: var(--font-size-xl);
        margin-bottom: var(--spacing-4);
    }

    .hero-description {
        font-size: var(--font-size-base);
        margin-bottom: var(--spacing-6);
    }

    .hero-buttons {
        justify-content: center;
        flex-direction: column;
        gap: var(--spacing-3);
    }

    .btn {
        min-height: 48px;
        padding: var(--spacing-3) var(--spacing-6);
        font-size: var(--font-size-base);
    }

    .image-placeholder,
    .hero-profile-image {
        width: 220px;
        height: 220px;
        font-size: 90px;
        margin: 0 auto;
    }

    .profile-image {
        width: 220px;
        height: 220px;
        margin: 0 auto;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
    }

    .feature-card {
        padding: var(--spacing-6);
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-4);
    }

    .section-title {
        font-size: var(--font-size-3xl);
        margin-bottom: var(--spacing-6);
    }

    .page-header {
        padding: var(--spacing-16) 0 var(--spacing-12);
    }

    .page-title {
        font-size: var(--font-size-3xl);
    }
}

/* Large phones (414px and below) */
@media (max-width: 414px) {
    .container {
        padding: 0 var(--spacing-4);
    }

    .hero {
        padding: var(--spacing-12) 0;
    }

    .hero-title {
        font-size: var(--font-size-2xl);
        line-height: 1.3;
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
    }

    .hero-description {
        font-size: var(--font-size-sm);
        line-height: 1.6;
    }

    .image-placeholder,
    .hero-profile-image {
        width: 180px;
        height: 180px;
        font-size: 70px;
    }

    .profile-image {
        width: 180px;
        height: 180px;
    }

    .section-title {
        font-size: var(--font-size-2xl);
        margin-bottom: var(--spacing-4);
    }

    .feature-card {
        padding: var(--spacing-4);
    }

    .feature-icon {
        font-size: var(--font-size-3xl);
        margin-bottom: var(--spacing-3);
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-3);
    }

    .stat-item {
        padding: var(--spacing-4);
    }

    .btn {
        width: 100%;
        justify-content: center;
        min-height: 44px;
        font-size: var(--font-size-sm);
    }

    .page-header {
        padding: var(--spacing-12) 0 var(--spacing-8);
    }

    .page-title {
        font-size: var(--font-size-2xl);
    }

    .page-subtitle {
        font-size: var(--font-size-base);
    }
}

/* Standard phones (375px and below) */
@media (max-width: 375px) {
    .hero-title {
        font-size: var(--font-size-xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-base);
    }

    .image-placeholder,
    .hero-profile-image {
        width: 160px;
        height: 160px;
        font-size: 60px;
    }

    .profile-image {
        width: 160px;
        height: 160px;
    }

    .feature-card h3 {
        font-size: var(--font-size-lg);
    }

    .feature-card p {
        font-size: var(--font-size-sm);
    }

    .stat-number {
        font-size: var(--font-size-2xl);
    }

    .stat-label {
        font-size: var(--font-size-sm);
    }
}

/* Small phones (320px and below) */
@media (max-width: 320px) {
    .container {
        padding: 0 var(--spacing-3);
    }

    .hero {
        padding: var(--spacing-10) 0;
    }

    .hero-title {
        font-size: var(--font-size-lg);
        margin-bottom: var(--spacing-3);
    }

    .hero-subtitle {
        font-size: var(--font-size-sm);
    }

    .hero-description {
        font-size: var(--font-size-xs);
    }

    .image-placeholder,
    .hero-profile-image {
        width: 140px;
        height: 140px;
        font-size: 50px;
    }

    .profile-image {
        width: 140px;
        height: 140px;
    }

    .section-title {
        font-size: var(--font-size-xl);
    }

    .feature-card {
        padding: var(--spacing-3);
    }

    .btn {
        min-height: 40px;
        padding: var(--spacing-2) var(--spacing-4);
        font-size: var(--font-size-xs);
    }

    .nav-link {
        font-size: var(--font-size-base);
        padding: var(--spacing-2) var(--spacing-3);
    }
}

/* Page Header Styles */
.page-header {
    padding: var(--spacing-20) 0 var(--spacing-16);
    background: linear-gradient(135deg, var(--background-color) 0%, var(--surface-color) 100%);
    text-align: center;
    margin-top: 70px;
}

.page-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-4);
    color: var(--text-primary);
}

.page-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
}

/* About Content Styles */
.about-content {
    padding: var(--spacing-20) 0;
    background: var(--background-color);
}

.about-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-16);
    align-items: center;
}

.about-text h2 {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-6);
    color: var(--primary-color);
}

.about-text p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: var(--spacing-6);
}

.personal-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-4);
    margin-top: var(--spacing-8);
}

.info-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3);
    background: var(--surface-color);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
}

.info-item i {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
    width: 20px;
}

.info-item span {
    color: var(--text-secondary);
}

.about-image {
    display: flex;
    justify-content: center;
}

.image-container {
    position: relative;
}

.profile-image {
    width: 250px;
    height: 250px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 100px;
    color: white;
    box-shadow: var(--shadow-xl);
    position: relative;
}

.profile-image::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: var(--gradient-secondary);
    border-radius: 50%;
    z-index: -1;
    opacity: 0.3;
}

.profile-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    transition: var(--transition-normal);
}

.profile-img:hover {
    transform: scale(1.05);
}

/* Skills Section */
.skills-section {
    padding: var(--spacing-20) 0;
    background: var(--surface-color);
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-8);
}

.skill-category {
    background: var(--background-color);
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-color);
}

.skill-category h3 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-6);
    color: var(--primary-color);
    text-align: center;
}

.skills-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
}

.skill-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
}

.skill-name {
    color: var(--text-primary);
    font-weight: 600;
}

.skill-bar {
    height: 8px;
    background: var(--surface-light);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.skill-progress {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-sm);
    transition: width 1s ease-in-out;
}

/* Experience Section */
.experience-section {
    padding: var(--spacing-20) 0;
    background: var(--background-color);
}

.timeline {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--primary-color);
    transform: translateX(-50%);
}

.timeline-item {
    display: flex;
    margin-bottom: var(--spacing-12);
    position: relative;
}

.timeline-item:nth-child(odd) {
    flex-direction: row;
}

.timeline-item:nth-child(even) {
    flex-direction: row-reverse;
}

.timeline-date {
    flex: 0 0 150px;
    text-align: center;
    color: var(--primary-color);
    font-weight: 600;
    padding: var(--spacing-4);
    background: var(--surface-color);
    border-radius: var(--radius-lg);
    border: 2px solid var(--primary-color);
    margin: 0 var(--spacing-4);
    position: relative;
    z-index: 1;
}

.timeline-content {
    flex: 1;
    background: var(--surface-color);
    padding: var(--spacing-6);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-color);
    margin: 0 var(--spacing-4);
}

.timeline-content h3 {
    color: var(--text-primary);
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-3);
}

.timeline-content p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-4);
    line-height: 1.6;
}

.timeline-content ul {
    list-style: none;
    padding-right: var(--spacing-4);
}

.timeline-content li {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-2);
    position: relative;
}

.timeline-content li::before {
    content: '•';
    color: var(--primary-color);
    position: absolute;
    right: -var(--spacing-4);
    font-weight: bold;
}

/* Education Section */
.education-section {
    padding: var(--spacing-20) 0;
    background: var(--surface-color);
}

.education-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-8);
}

.education-item {
    background: var(--background-color);
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    text-align: center;
    border: 1px solid var(--border-color);
    transition: var(--transition-normal);
}

.education-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.education-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-6);
    font-size: var(--font-size-2xl);
    color: white;
}

.education-item h3 {
    color: var(--text-primary);
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-3);
}

.education-place {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: var(--spacing-2);
}

.education-date {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-4);
}

.education-item p:last-child {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Projects Page Styles */
.filter-section {
    padding: var(--spacing-8) 0;
    background: var(--surface-color);
}

.filter-buttons {
    display: flex;
    justify-content: center;
    gap: var(--spacing-4);
    flex-wrap: wrap;
}

.filter-btn {
    padding: var(--spacing-3) var(--spacing-6);
    background: transparent;
    border: 2px solid var(--border-color);
    color: var(--text-secondary);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: var(--transition-normal);
    font-family: var(--font-family);
    font-weight: 600;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.projects-section {
    padding: var(--spacing-20) 0;
    background: var(--background-color);
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-8);
}

.project-card {
    background: var(--surface-color);
    border-radius: var(--radius-xl);
    overflow: hidden;
    border: 1px solid var(--border-color);
    transition: var(--transition-normal);
}

.project-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.project-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.project-placeholder {
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 60px;
    color: white;
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-normal);
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-actions {
    display: flex;
    gap: var(--spacing-4);
}

.action-btn {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: var(--transition-fast);
}

.action-btn:hover {
    background: var(--primary-dark);
    transform: scale(1.1);
}

.project-content {
    padding: var(--spacing-6);
}

.project-title {
    font-size: var(--font-size-xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-3);
    font-weight: 600;
}

.project-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-4);
}

.project-tech {
    display: flex;
    gap: var(--spacing-2);
    flex-wrap: wrap;
    margin-bottom: var(--spacing-4);
}

.tech-tag {
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.project-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    flex-wrap: wrap;
    gap: var(--spacing-2);
}

.project-date,
.project-client {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.project-meta i {
    color: var(--primary-color);
}

/* Call to Action Section */
.cta-section {
    padding: var(--spacing-20) 0;
    background: var(--surface-color);
    text-align: center;
}

.cta-content h2 {
    font-size: var(--font-size-4xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-4);
}

.cta-content p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-8);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Services Page Styles */
.services-section {
    padding: var(--spacing-20) 0;
    background: var(--background-color);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-8);
}

.service-card {
    background: var(--surface-color);
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-color);
    transition: var(--transition-normal);
    position: relative;
    text-align: center;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.service-card.featured {
    border-color: var(--secondary-color);
    background: linear-gradient(135deg, var(--surface-color) 0%, rgba(16, 185, 129, 0.05) 100%);
}

.featured-badge {
    position: absolute;
    top: -10px;
    right: var(--spacing-4);
    background: var(--secondary-color);
    color: white;
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.service-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-6);
    font-size: var(--font-size-2xl);
    color: white;
}

.service-card.featured .service-icon {
    background: var(--gradient-secondary);
}

.service-title {
    font-size: var(--font-size-xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-4);
    font-weight: 600;
}

.service-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-6);
}

.service-features {
    list-style: none;
    margin-bottom: var(--spacing-6);
    text-align: right;
}

.service-features li {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-3);
    color: var(--text-secondary);
}

.service-features i {
    color: var(--secondary-color);
    font-size: var(--font-size-sm);
}

.service-price {
    margin-bottom: var(--spacing-6);
    text-align: center;
}

.price-from {
    display: block;
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-2);
}

.price-amount {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-color);
}

.service-btn {
    display: block;
    width: 100%;
    padding: var(--spacing-3) var(--spacing-6);
    background: var(--gradient-primary);
    color: white;
    text-decoration: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    transition: var(--transition-normal);
    text-align: center;
}

.service-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.service-card.featured .service-btn {
    background: var(--gradient-secondary);
}

/* Process Section */
.process-section {
    padding: var(--spacing-20) 0;
    background: var(--surface-color);
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-8);
}

.process-step {
    text-align: center;
    position: relative;
}

.step-number {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: white;
    margin: 0 auto var(--spacing-4);
}

.step-content h3 {
    font-size: var(--font-size-xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-3);
}

.step-content p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* FAQ Section */
.faq-section {
    padding: var(--spacing-20) 0;
    background: var(--background-color);
}

.faq-grid {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: var(--surface-color);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-4);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.faq-question {
    padding: var(--spacing-6);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition-fast);
}

.faq-question:hover {
    background: var(--surface-light);
}

.faq-question h3 {
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    margin: 0;
}

.faq-question i {
    color: var(--primary-color);
    transition: var(--transition-fast);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
}

.faq-answer p {
    padding: 0 var(--spacing-6) var(--spacing-6);
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
}

/* Products Page Styles */
.products-section {
    padding: var(--spacing-20) 0;
    background: var(--background-color);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-8);
}

.product-card {
    background: var(--surface-color);
    border-radius: var(--radius-xl);
    overflow: hidden;
    border: 1px solid var(--border-color);
    transition: var(--transition-normal);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.product-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.product-placeholder {
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 60px;
    color: white;
}

.product-badge {
    position: absolute;
    top: var(--spacing-4);
    right: var(--spacing-4);
    background: var(--secondary-color);
    color: white;
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.product-badge.bestseller {
    background: var(--accent-color);
}

.product-content {
    padding: var(--spacing-6);
}

.product-title {
    font-size: var(--font-size-xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-3);
    font-weight: 600;
}

.product-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-4);
}

.product-features {
    display: flex;
    gap: var(--spacing-2);
    flex-wrap: wrap;
    margin-bottom: var(--spacing-4);
}

.feature-tag {
    background: var(--surface-light);
    color: var(--text-secondary);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
}

.feature-tag i {
    color: var(--secondary-color);
    font-size: 10px;
}

.product-price {
    margin-bottom: var(--spacing-6);
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.old-price {
    color: var(--text-muted);
    text-decoration: line-through;
    font-size: var(--font-size-sm);
}

.current-price {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-color);
}

.product-actions {
    display: flex;
    gap: var(--spacing-3);
}

.product-actions .btn {
    flex: 1;
    justify-content: center;
    padding: var(--spacing-3) var(--spacing-4);
    font-size: var(--font-size-sm);
}

/* Product Features Section */
.product-features-section {
    padding: var(--spacing-20) 0;
    background: var(--surface-color);
}

.product-features-section .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-8);
}

.feature-item {
    text-align: center;
    padding: var(--spacing-6);
}

.feature-item .feature-icon {
    width: 70px;
    height: 70px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-4);
    font-size: var(--font-size-xl);
    color: white;
}

.feature-item h3 {
    font-size: var(--font-size-lg);
    color: var(--text-primary);
    margin-bottom: var(--spacing-3);
}

.feature-item p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Contact Page Styles */
.contact-section {
    padding: var(--spacing-20) 0;
    background: var(--background-color);
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-16);
    align-items: start;
}

.contact-form-container,
.contact-info-container {
    background: var(--surface-color);
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-color);
}

.contact-form-container h2,
.contact-info-container h2 {
    color: var(--text-primary);
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-6);
    text-align: center;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
}

.form-group label {
    color: var(--text-primary);
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: var(--spacing-3);
    background: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    transition: var(--transition-fast);
    min-height: 44px; /* Minimum touch target size */
    -webkit-appearance: none; /* Remove default styling on iOS */
    -moz-appearance: none; /* Remove default styling on Firefox */
    appearance: none; /* Remove default styling */
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.contact-info-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

.contact-info-item {
    display: flex;
    gap: var(--spacing-4);
    align-items: flex-start;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-lg);
    flex-shrink: 0;
}

.contact-details h3 {
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-2);
}

.contact-details p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.social-section {
    border-top: 1px solid var(--border-color);
    padding-top: var(--spacing-6);
}

.social-section h3 {
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-4);
    text-align: center;
}

.social-links-large {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
}

.social-link-large {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3);
    background: var(--background-color);
    border-radius: var(--radius-lg);
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-fast);
    border: 1px solid var(--border-color);
}

.social-link-large:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateX(-5px);
}

.social-link-large i {
    font-size: var(--font-size-lg);
    width: 20px;
}

/* Map Section */
.map-section {
    padding: var(--spacing-20) 0;
    background: var(--surface-color);
}

.map-container {
    background: var(--background-color);
    border-radius: var(--radius-xl);
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.map-placeholder {
    height: 400px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    text-align: center;
    gap: var(--spacing-4);
}

.map-placeholder i {
    font-size: 60px;
    color: var(--primary-color);
}

.map-placeholder p {
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.map-placeholder small {
    color: var(--text-muted);
}

/* Contact FAQ Section */
.contact-faq-section {
    padding: var(--spacing-20) 0;
    background: var(--background-color);
}

/* Responsive Design for Contact Page */
@media (max-width: 768px) {
    .contact-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-8);
    }

    .about-grid {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-8);
    }

    .timeline::before {
        left: 30px;
    }

    .timeline-item {
        flex-direction: column;
        margin-left: 60px;
    }

    .timeline-item:nth-child(even) {
        flex-direction: column;
    }

    .timeline-date {
        flex: none;
        width: auto;
        margin: 0 0 var(--spacing-4) 0;
    }

    .timeline-content {
        margin: 0;
    }

    .skills-grid {
        grid-template-columns: 1fr;
    }

    .filter-buttons {
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: var(--spacing-2);
    }

    .filter-btn {
        white-space: nowrap;
        flex-shrink: 0;
    }

    .projects-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
    }

    .project-card,
    .service-card,
    .product-card {
        margin-bottom: var(--spacing-4);
    }

    .process-steps {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }

    .product-actions {
        flex-direction: column;
        gap: var(--spacing-3);
    }

    .product-actions .btn {
        width: 100%;
    }

    .social-links-large {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-3);
    }

    .contact-info-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-3);
        padding: var(--spacing-4);
    }

    .contact-icon {
        margin: 0 auto;
        font-size: var(--font-size-2xl);
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-8);
        text-align: center;
    }

    .about-text {
        order: 2;
    }

    .about-image {
        order: 1;
        justify-self: center;
    }

    .skills-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }
}

/* Enhanced responsive design for smaller screens */
@media (max-width: 414px) {
    .page-title {
        font-size: var(--font-size-2xl);
        margin-bottom: var(--spacing-3);
    }

    .hero-buttons {
        flex-direction: column;
        width: 100%;
        gap: var(--spacing-3);
    }

    .hero-buttons .btn {
        width: 100%;
        justify-content: center;
        min-height: 44px;
    }

    .personal-info {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }

    .project-card,
    .service-card,
    .product-card {
        padding: var(--spacing-4);
    }

    .project-title,
    .service-title,
    .product-title {
        font-size: var(--font-size-lg);
    }

    .project-description,
    .service-description,
    .product-description {
        font-size: var(--font-size-sm);
        line-height: 1.6;
    }

    .filter-buttons {
        gap: var(--spacing-2);
    }

    .filter-btn {
        padding: var(--spacing-2) var(--spacing-3);
        font-size: var(--font-size-sm);
    }

    .contact-form {
        padding: var(--spacing-4);
    }

    .form-group {
        margin-bottom: var(--spacing-4);
    }

    .form-input,
    .form-textarea {
        padding: var(--spacing-3);
        font-size: var(--font-size-base);
    }
}

@media (max-width: 375px) {
    .project-card,
    .service-card,
    .product-card {
        padding: var(--spacing-3);
    }

    .project-title,
    .service-title,
    .product-title {
        font-size: var(--font-size-base);
    }

    .filter-btn {
        padding: var(--spacing-1) var(--spacing-2);
        font-size: var(--font-size-xs);
    }

    .contact-info-item {
        padding: var(--spacing-3);
    }
}

@media (max-width: 320px) {
    .page-title {
        font-size: var(--font-size-xl);
    }

    .project-card,
    .service-card,
    .product-card {
        padding: var(--spacing-2);
        margin-bottom: var(--spacing-3);
    }

    .hero-buttons .btn {
        min-height: 40px;
        font-size: var(--font-size-xs);
    }

    .contact-form {
        padding: var(--spacing-3);
    }

    .form-input,
    .form-textarea {
        padding: var(--spacing-2);
        font-size: var(--font-size-sm);
    }
}

    .education-grid,
    .product-features-section .features-grid {
        grid-template-columns: 1fr;
    }

    .project-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-1);
    }

    .social-links-large {
        grid-template-columns: 1fr;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        font-size: 16px; /* Prevents zoom on iOS */
    }
}
